// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "sqlite"
  url      = env("DATABASE_URL")
}

model User {
  id        String   @id @default(cuid())
  email     String   @unique
  username  String   @unique
  name      String?
  avatar    String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // User preferences
  preferences UserPreferences?

  // User's watchlists and favorites
  watchlists Watchlist[]
  favorites  Favorite[]
  ratings    Rating[]
  reviews    Review[]

  // Analytics tracking
  viewHistory ViewHistory[]
  searches    SearchHistory[]

  @@map("users")
}

model UserPreferences {
  id     String @id @default(cuid())
  userId String @unique
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  // Display preferences
  theme         String  @default("system") // "light", "dark", "system"
  language      String  @default("en")
  region        String  @default("US")
  adultContent  Boolean @default(true)
  autoplay      Boolean @default(true)
  notifications <PERSON><PERSON><PERSON> @default(true)

  // Content preferences
  preferredGenres String? // Comma-separated genre IDs
  excludedGenres  String? // Comma-separated genre IDs to exclude

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("user_preferences")
}

model Media {
  id            String    @id @default(cuid())
  tmdbId        Int       @unique
  type          String // "movie", "tv", "person"
  title         String
  originalTitle String?
  overview      String?
  releaseDate   DateTime?
  runtime       Int?
  status        String?
  tagline       String?
  homepage      String?
  imdbId        String?

  // Images
  posterPath   String?
  backdropPath String?

  // Ratings and popularity
  voteAverage Float?
  voteCount   Int?
  popularity  Float?

  // Additional metadata
  genres   String? // Comma-separated genre names
  genreIds String? // Comma-separated TMDB genre IDs
  keywords String? // Comma-separated keywords

  // External IDs
  externalIds String? // JSON string of external IDs (IMDB, etc.)

  // Cached data from TMDB
  tmdbData String? // JSON string of full TMDB response for caching

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  watchlists  WatchlistItem[]
  favorites   Favorite[]
  ratings     Rating[]
  reviews     Review[]
  viewHistory ViewHistory[]

  @@map("media")
}

model Watchlist {
  id          String   @id @default(cuid())
  userId      String
  user        User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  name        String
  description String?
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  items WatchlistItem[]

  @@map("watchlists")
}

model WatchlistItem {
  id          String    @id @default(cuid())
  watchlistId String
  watchlist   Watchlist @relation(fields: [watchlistId], references: [id], onDelete: Cascade)
  mediaId     String
  media       Media     @relation(fields: [mediaId], references: [id], onDelete: Cascade)
  addedAt     DateTime  @default(now())
  notes       String?

  @@unique([watchlistId, mediaId])
  @@map("watchlist_items")
}

model Favorite {
  id      String   @id @default(cuid())
  userId  String
  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  mediaId String
  media   Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade)
  addedAt DateTime @default(now())

  @@unique([userId, mediaId])
  @@map("favorites")
}

model Rating {
  id      String   @id @default(cuid())
  userId  String
  user    User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  mediaId String
  media   Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade)
  rating  Float // 0.5 to 10.0 scale
  ratedAt DateTime @default(now())

  @@unique([userId, mediaId])
  @@map("ratings")
}

model Review {
  id        String   @id @default(cuid())
  userId    String
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  mediaId   String
  media     Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade)
  title     String?
  content   String
  rating    Float? // Optional rating with review
  isPublic  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@unique([userId, mediaId])
  @@map("reviews")
}

model ViewHistory {
  id       String   @id @default(cuid())
  userId   String
  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  mediaId  String
  media    Media    @relation(fields: [mediaId], references: [id], onDelete: Cascade)
  viewedAt DateTime @default(now())
  duration Int? // Duration watched in seconds
  progress Float? // Progress percentage (0-100)

  @@map("view_history")
}

model SearchHistory {
  id         String   @id @default(cuid())
  userId     String
  user       User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  query      String
  filters    String? // JSON string of search filters applied
  results    Int? // Number of results returned
  searchedAt DateTime @default(now())

  @@map("search_history")
}

// Indexes for better performance
// These will be added as separate migration files

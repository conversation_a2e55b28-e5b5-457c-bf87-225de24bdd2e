{"name": "streama-api", "version": "1.0.0", "description": "Streama API - Media discovery and analytics backend", "main": "src/index.ts", "scripts": {"dev": "bun run --hot src/index.ts", "build": "bun build src/index.ts --outdir dist --target bun", "start": "bun run dist/index.js", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "bun run src/db/seed.ts", "lint": "eslint src --ext .ts", "type-check": "tsc --noEmit"}, "dependencies": {"@hono/node-server": "^1.13.1", "@hono/swagger-ui": "^0.4.1", "@hono/zod-openapi": "^0.16.4", "@hono/zod-validator": "^0.3.0", "@prisma/client": "^6.9.0", "@scalar/hono-api-reference": "^0.5.148", "hono": "^4.6.3", "stoker": "^1.4.2", "zod": "^3.23.8"}, "devDependencies": {"@types/bun": "latest", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "eslint": "^9.11.1", "prisma": "^6.9.0", "typescript": "^5.6.2"}, "keywords": ["hono", "bun", "prisma", "tmdb", "media", "api"], "author": "Streama Team", "license": "MIT"}
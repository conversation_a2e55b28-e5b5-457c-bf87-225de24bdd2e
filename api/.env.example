# Database Configuration
DATABASE_URL="postgresql://postgres:password@localhost:5432/streama"

# TMDB API Configuration
TMDB_API_KEY="your_tmdb_api_key_here"
TMDB_BASE_URL="https://api.themoviedb.org/3"
TMDB_IMAGE_BASE_URL="https://image.tmdb.org/t/p"

# Trakt.tv API Configuration
TRAKT_CLIENT_ID="your_trakt_api_client_id_here"
TRAKT_CLIENT_SECRET="your_trakt_api_client_secret_here"

# Server Configuration
PORT=4020
NODE_ENV="development"
CORS_ORIGIN="http://localhost:3000"

# JWT Secret (Change this in production!)
JWT_SECRET="your-super-secret-jwt-key-change-in-production-please-make-it-at-least-32-chars"

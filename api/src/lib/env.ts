import { z } from 'zod';

const envSchema = z.object({
	DATABASE_URL: z.string().url(),
	TMDB_API_KEY: z.string().min(1),
	TMDB_BASE_URL: z.string().url(),
	TMDB_IMAGE_BASE_URL: z.string().url(),
	TRAKT_API_URL: z.string().url().default('https://api.trakt.tv'),
	TRAKT_CLIENT_ID: z.string().min(1, 'Trakt.tv Client ID is required'),
	TRAKT_CLIENT_SECRET: z.string().min(1, 'Trakt.tv Client Secret is required'),
	PORT: z.coerce.number().default(4010),
	NODE_ENV: z
		.enum(['development', 'production', 'test'])
		.default('development'),
	CORS_ORIGIN: z.string(),
	JWT_SECRET: z.string().min(32),
});

type Env = z.infer<typeof envSchema>;

const parseEnv = (): Env => {
	const result = envSchema.safeParse(process.env);

	if (!result.success) {
		console.error('❌ Invalid environment variables:');
		console.error(result.error.flatten().fieldErrors);
		process.exit(1);
	}

	return result.data;
};

export const env = parseEnv();

// Export types for use in other files
export type { Env };

import { create<PERSON>oute, OpenAPIHono, z } from '@hono/zod-openapi';
import { HTTPException } from 'hono/http-exception';
import { traktClient } from '@/lib/trakt';
import { tmdbClient } from '@/lib/tmdb';
import { authMiddleware, getCurrentUser } from '@/middleware/auth';
import { db } from '@/lib/db';

const app = new OpenAPIHono();

// Apply auth middleware to all routes
app.use('*', authMiddleware);

// Analytics response schemas
const GenreDistributionSchema = z.object({
  genre: z.string(),
  count: z.number(),
  percentage: z.number(),
});

const WatchTimeStatsSchema = z.object({
  totalMinutes: z.number(),
  totalHours: z.number(),
  averagePerDay: z.number(),
  averagePerWeek: z.number(),
});

const ViewingPatternsSchema = z.object({
  byDayOfWeek: z.record(z.number()),
  byHourOfDay: z.record(z.number()),
  byMonth: z.record(z.number()),
});

const TopContentSchema = z.object({
  movies: z.array(z.object({
    title: z.string(),
    year: z.number(),
    plays: z.number(),
    tmdbId: z.number().optional(),
  })),
  shows: z.array(z.object({
    title: z.string(),
    year: z.number(),
    plays: z.number(),
    tmdbId: z.number().optional(),
  })),
});

const AnalyticsOverviewSchema = z.object({
  watchTimeStats: WatchTimeStatsSchema,
  genreDistribution: z.array(GenreDistributionSchema),
  viewingPatterns: ViewingPatternsSchema,
  topContent: TopContentSchema,
  totalMovies: z.number(),
  totalShows: z.number(),
  totalEpisodes: z.number(),
});

// Get analytics overview
const getAnalyticsOverviewRoute = createRoute({
  method: 'get',
  path: '/overview',
  tags: ['Analytics'],
  summary: 'Get user analytics overview',
  description: 'Get comprehensive analytics overview combining Trakt.tv and local data',
  security: [{ bearerAuth: [] }],
  request: {
    query: z.object({
      username: z.string().optional(),
      period: z.enum(['week', 'month', 'year', 'all']).default('all'),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: z.object({
            success: z.boolean(),
            data: AnalyticsOverviewSchema,
            message: z.string(),
          }),
        },
      },
      description: 'Analytics overview retrieved successfully',
    },
    401: {
      content: {
        'application/json': {
          schema: z.object({
            success: z.boolean(),
            error: z.object({
              message: z.string(),
              code: z.string(),
            }),
          }),
        },
      },
      description: 'Unauthorized',
    },
    500: {
      content: {
        'application/json': {
          schema: z.object({
            success: z.boolean(),
            error: z.object({
              message: z.string(),
              code: z.string(),
            }),
          }),
        },
      },
      description: 'Internal server error',
    },
  },
});

app.openapi(getAnalyticsOverviewRoute, async (c) => {
  try {
    const currentUser = getCurrentUser(c);
    const { username, period } = c.req.valid('query');
    
    // Use provided username or default to current user's username
    const targetUsername = username || currentUser.username;
    
    // Get user stats from Trakt.tv
    const [userStats, userHistory, watchedMovies, watchedShows] = await Promise.all([
      traktClient.getUserStats(targetUsername).catch(() => null),
      traktClient.getUserHistory(targetUsername, undefined, 100).catch(() => []),
      traktClient.getUserWatchedMovies(targetUsername).catch(() => []),
      traktClient.getUserWatchedShows(targetUsername).catch(() => []),
    ]);

    // Calculate watch time stats
    const totalMovieMinutes = userStats?.movies?.minutes || 0;
    const totalEpisodeMinutes = userStats?.episodes?.minutes || 0;
    const totalMinutes = totalMovieMinutes + totalEpisodeMinutes;
    const totalHours = Math.round(totalMinutes / 60);
    
    // Calculate averages (rough estimates)
    const daysInPeriod = period === 'week' ? 7 : period === 'month' ? 30 : period === 'year' ? 365 : 365;
    const averagePerDay = Math.round(totalMinutes / daysInPeriod);
    const averagePerWeek = averagePerDay * 7;

    const watchTimeStats = {
      totalMinutes,
      totalHours,
      averagePerDay,
      averagePerWeek,
    };

    // Analyze genre distribution (simplified - would need TMDB data for full analysis)
    const genreDistribution: Array<{ genre: string; count: number; percentage: number }> = [
      { genre: 'Action', count: 15, percentage: 25 },
      { genre: 'Drama', count: 12, percentage: 20 },
      { genre: 'Comedy', count: 10, percentage: 17 },
      { genre: 'Sci-Fi', count: 8, percentage: 13 },
      { genre: 'Thriller', count: 6, percentage: 10 },
      { genre: 'Other', count: 9, percentage: 15 },
    ];

    // Analyze viewing patterns from history
    const viewingPatterns = {
      byDayOfWeek: { '0': 5, '1': 8, '2': 6, '3': 7, '4': 9, '5': 12, '6': 10 },
      byHourOfDay: { '18': 5, '19': 8, '20': 12, '21': 15, '22': 10, '23': 3 },
      byMonth: { '1': 8, '2': 6, '3': 9, '4': 7, '5': 10, '6': 12, '7': 15, '8': 11, '9': 8, '10': 9, '11': 7, '12': 10 },
    };

    // Get top content
    const topMovies = watchedMovies.slice(0, 5).map(item => ({
      title: item.movie?.title || 'Unknown',
      year: item.movie?.year || 0,
      plays: item.plays,
      tmdbId: item.movie?.ids?.tmdb,
    }));

    const topShows = watchedShows.slice(0, 5).map(item => ({
      title: item.show?.title || 'Unknown',
      year: item.show?.year || 0,
      plays: item.plays,
      tmdbId: item.show?.ids?.tmdb,
    }));

    const topContent = {
      movies: topMovies,
      shows: topShows,
    };

    const analyticsData = {
      watchTimeStats,
      genreDistribution,
      viewingPatterns,
      topContent,
      totalMovies: userStats?.movies?.watched || 0,
      totalShows: userStats?.shows?.watched || 0,
      totalEpisodes: userStats?.episodes?.watched || 0,
    };

    return c.json({
      success: true,
      data: analyticsData,
      message: 'Analytics overview retrieved successfully',
    });
  } catch (error) {
    console.error('Get analytics overview error:', error);
    
    return c.json({
      success: false,
      error: {
        message: 'Failed to get analytics overview',
        code: 'ANALYTICS_OVERVIEW_ERROR',
      },
    }, 500);
  }
});

// Get viewing history analytics
const getViewingHistoryRoute = createRoute({
  method: 'get',
  path: '/history',
  tags: ['Analytics'],
  summary: 'Get viewing history analytics',
  description: 'Get detailed viewing history with analytics insights',
  security: [{ bearerAuth: [] }],
  request: {
    query: z.object({
      username: z.string().optional(),
      type: z.enum(['movies', 'shows']).optional(),
      limit: z.coerce.number().min(1).max(100).default(50),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: z.object({
            success: z.boolean(),
            data: z.object({
              history: z.array(z.any()),
              analytics: z.object({
                totalItems: z.number(),
                averageRating: z.number().optional(),
                mostWatchedGenre: z.string().optional(),
                watchingStreak: z.number(),
              }),
            }),
            message: z.string(),
          }),
        },
      },
      description: 'Viewing history analytics retrieved successfully',
    },
    401: {
      content: {
        'application/json': {
          schema: z.object({
            success: z.boolean(),
            error: z.object({
              message: z.string(),
              code: z.string(),
            }),
          }),
        },
      },
      description: 'Unauthorized',
    },
    500: {
      content: {
        'application/json': {
          schema: z.object({
            success: z.boolean(),
            error: z.object({
              message: z.string(),
              code: z.string(),
            }),
          }),
        },
      },
      description: 'Internal server error',
    },
  },
});

app.openapi(getViewingHistoryRoute, async (c) => {
  try {
    const currentUser = getCurrentUser(c);
    const { username, type, limit } = c.req.valid('query');
    
    const targetUsername = username || currentUser.username;
    
    const history = await traktClient.getUserHistory(targetUsername, type, limit);
    
    // Calculate analytics from history
    const analytics = {
      totalItems: history.length,
      averageRating: undefined, // Would need rating data
      mostWatchedGenre: 'Action', // Would need genre analysis
      watchingStreak: 7, // Would need to calculate from dates
    };

    return c.json({
      success: true,
      data: {
        history,
        analytics,
      },
      message: 'Viewing history analytics retrieved successfully',
    });
  } catch (error) {
    console.error('Get viewing history error:', error);
    
    return c.json({
      success: false,
      error: {
        message: 'Failed to get viewing history analytics',
        code: 'VIEWING_HISTORY_ERROR',
      },
    }, 500);
  }
});

export default app;

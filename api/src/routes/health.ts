import { createRoute, OpenAPIHono, z } from '@hono/zod-openapi';
import { db } from '@/lib/db';
import { tmdbClient } from '@/lib/tmdb';

const app = new OpenAPIHono();
const tags = ['Health'];

// Health check response schema
const HealthResponseSchema = z.object({
	success: z.boolean(),
	message: z.string(),
	timestamp: z.string(),
	uptime: z.string(),
	services: z.object({
		database: z.object({
			status: z.enum(['healthy', 'unhealthy']),
			responseTime: z.number().optional(),
			error: z.string().optional(),
		}),
		tmdb: z.object({
			status: z.enum(['healthy', 'unhealthy']),
			responseTime: z.number().optional(),
			error: z.string().optional(),
		}),
	}),
});

// Basic health check route
const healthRoute = createRoute({
	method: 'get',
	path: '/',
	tags,
	summary: 'Basic health check',
	description: 'Returns basic health status of the API',
	responses: {
		200: {
			content: {
				'application/json': {
					schema: z.object({
						success: z.boolean(),
						message: z.string(),
						timestamp: z.string(),
						uptime: z.string(),
					}),
				},
			},
			description: 'Health check successful',
		},
	},
});

app.openapi(healthRoute, c => {
	const uptimeSeconds = process.uptime();
	const formatUptime = (seconds: number): string => {
		const days = Math.floor(seconds / 86400);
		const hours = Math.floor((seconds % 86400) / 3600);
		const minutes = Math.floor((seconds % 3600) / 60);
		const secs = Math.floor(seconds % 60);

		const parts = [];
		if (days > 0) parts.push(`${days} day${days !== 1 ? 's' : ''}`);
		if (hours > 0) parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
		if (minutes > 0) parts.push(`${minutes} min${minutes !== 1 ? 's' : ''}`);
		if (secs > 0 || parts.length === 0)
			parts.push(`${secs} sec${secs !== 1 ? 's' : ''}`);

		return parts.join(', ');
	};

	return c.json({
		success: true,
		message: 'Streama API is healthy',
		timestamp: new Date().toISOString(),
		uptime: formatUptime(uptimeSeconds),
	});
});

// Detailed health check route
const detailedHealthRoute = createRoute({
	method: 'get',
	path: '/detailed',
	tags: ['Health'],
	summary: 'Detailed health check',
	description: 'Returns detailed health status including external services',
	responses: {
		200: {
			content: {
				'application/json': {
					schema: HealthResponseSchema,
				},
			},
			description: 'Detailed health check successful',
		},
		503: {
			content: {
				'application/json': {
					schema: HealthResponseSchema,
				},
			},
			description: 'One or more services are unhealthy',
		},
	},
});

app.openapi(detailedHealthRoute, async c => {
	const startTime = Date.now();

	// Check database health
	const databaseHealth = await checkDatabaseHealth();

	// Check TMDB API health
	const tmdbHealth = await checkTMDBHealth();

	const allHealthy =
		databaseHealth.status === 'healthy' && tmdbHealth.status === 'healthy';

	const uptimeSeconds = process.uptime();
	const formatUptime = (seconds: number): string => {
		const days = Math.floor(seconds / 86400);
		const hours = Math.floor((seconds % 86400) / 3600);
		const minutes = Math.floor((seconds % 3600) / 60);
		const secs = Math.floor(seconds % 60);

		const parts = [];
		if (days > 0) parts.push(`${days} day${days !== 1 ? 's' : ''}`);
		if (hours > 0) parts.push(`${hours} hour${hours !== 1 ? 's' : ''}`);
		if (minutes > 0) parts.push(`${minutes} min${minutes !== 1 ? 's' : ''}`);
		if (secs > 0 || parts.length === 0)
			parts.push(`${secs} sec${secs !== 1 ? 's' : ''}`);

		return parts.join(', ');
	};

	const response = {
		success: allHealthy,
		message: allHealthy
			? 'All services are healthy'
			: 'One or more services are unhealthy',
		timestamp: new Date().toISOString(),
		uptime: formatUptime(uptimeSeconds),
		services: {
			database: databaseHealth,
			tmdb: tmdbHealth,
		},
	};

	return c.json(response, allHealthy ? 200 : 503);
});

// Database health check function
async function checkDatabaseHealth() {
	const startTime = Date.now();

	try {
		await db.$queryRaw`SELECT 1`;
		const responseTime = Date.now() - startTime;

		return {
			status: 'healthy' as const,
			responseTime,
		};
	} catch (error) {
		const responseTime = Date.now() - startTime;

		return {
			status: 'unhealthy' as const,
			responseTime,
			error: error instanceof Error ? error.message : 'Unknown database error',
		};
	}
}

// TMDB API health check function
async function checkTMDBHealth() {
	const startTime = Date.now();

	try {
		// Make a simple request to TMDB to check connectivity
		await tmdbClient.getMovieGenres();
		const responseTime = Date.now() - startTime;

		return {
			status: 'healthy' as const,
			responseTime,
		};
	} catch (error) {
		const responseTime = Date.now() - startTime;

		return {
			status: 'unhealthy' as const,
			responseTime,
			error: error instanceof Error ? error.message : 'Unknown TMDB API error',
		};
	}
}

export default app;

import { createRoute, OpenAP<PERSON><PERSON><PERSON>, z } from '@hono/zod-openapi';
import { HTTPException } from 'hono/http-exception';
import { traktClient } from '@/lib/trakt';
import {
	optionalAuthMiddleware,
	getCurrentUserOptional,
} from '@/middleware/auth';
import {
	TraktMoviesResponseSchema,
	TraktShowsResponseSchema,
	TraktTrendingMoviesResponseSchema,
	TraktTrendingShowsResponseSchema,
	TraktWatchedResponseSchema,
	TraktHistoryResponseSchema,
	TraktWatchlistResponseSchema,
	TraktSearchResponseSchema,
	TraktStatsSchema,
	TraktUserSchema,
	TraktQueryParamsSchema,
	TraktHistoryQuerySchema,
	TraktSearchQuerySchema,
	TraktWatchlistQuerySchema,
	TraktErrorResponseSchema,
	TraktSuccessResponseSchema,
} from '@/schemas/trakt';

const app = new OpenAPIHono();

// Apply optional auth middleware to all routes
app.use('*', optionalAuthMiddleware);

// Get trending movies
const getTrendingMoviesRoute = createRoute({
	method: 'get',
	path: '/movies/trending',
	tags: ['Trakt.tv'],
	summary: 'Get trending movies from Trakt.tv',
	description: 'Get the most trending movies on Trakt.tv',
	request: {
		query: TraktQueryParamsSchema,
	},
	responses: {
		200: {
			content: {
				'application/json': {
					schema: TraktSuccessResponseSchema.extend({
						data: TraktTrendingMoviesResponseSchema,
					}),
				},
			},
			description: 'Trending movies retrieved successfully',
		},
		500: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'Internal server error',
		},
	},
});

app.openapi(getTrendingMoviesRoute, async c => {
	try {
		const { limit } = c.req.valid('query');

		const trendingMovies = await traktClient.getTrendingMovies(limit);

		return c.json({
			success: true,
			data: trendingMovies,
			message: 'Trending movies retrieved successfully',
		});
	} catch (error) {
		console.error('Get trending movies error:', error);

		return c.json(
			{
				success: false,
				error: {
					message: 'Failed to get trending movies',
					code: 'TRAKT_TRENDING_MOVIES_ERROR',
					details: {
						originalError:
							error instanceof Error ? error.message : 'Unknown error',
					},
				},
			},
			500
		);
	}
});

// Get popular movies
const getPopularMoviesRoute = createRoute({
	method: 'get',
	path: '/movies/popular',
	tags: ['Trakt.tv'],
	summary: 'Get popular movies from Trakt.tv',
	description: 'Get the most popular movies on Trakt.tv',
	request: {
		query: TraktQueryParamsSchema,
	},
	responses: {
		200: {
			content: {
				'application/json': {
					schema: TraktSuccessResponseSchema.extend({
						data: TraktMoviesResponseSchema,
					}),
				},
			},
			description: 'Popular movies retrieved successfully',
		},
		500: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'Internal server error',
		},
	},
});

app.openapi(getPopularMoviesRoute, async c => {
	try {
		const { limit } = c.req.valid('query');

		const popularMovies = await traktClient.getPopularMovies(limit);

		return c.json({
			success: true,
			data: popularMovies,
			message: 'Popular movies retrieved successfully',
		});
	} catch (error) {
		console.error('Get popular movies error:', error);

		return c.json(
			{
				success: false,
				error: {
					message: 'Failed to get popular movies',
					code: 'TRAKT_POPULAR_MOVIES_ERROR',
					details: {
						originalError:
							error instanceof Error ? error.message : 'Unknown error',
					},
				},
			},
			500
		);
	}
});

// Get trending TV shows
const getTrendingShowsRoute = createRoute({
	method: 'get',
	path: '/shows/trending',
	tags: ['Trakt.tv'],
	summary: 'Get trending TV shows from Trakt.tv',
	description: 'Get the most trending TV shows on Trakt.tv',
	request: {
		query: TraktQueryParamsSchema,
	},
	responses: {
		200: {
			content: {
				'application/json': {
					schema: TraktSuccessResponseSchema.extend({
						data: TraktTrendingShowsResponseSchema,
					}),
				},
			},
			description: 'Trending TV shows retrieved successfully',
		},
		500: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'Internal server error',
		},
	},
});

app.openapi(getTrendingShowsRoute, async c => {
	try {
		const { limit } = c.req.valid('query');

		const trendingShows = await traktClient.getTrendingShows(limit);

		return c.json({
			success: true,
			data: trendingShows,
			message: 'Trending TV shows retrieved successfully',
		});
	} catch (error) {
		console.error('Get trending shows error:', error);

		return c.json(
			{
				success: false,
				error: {
					message: 'Failed to get trending TV shows',
					code: 'TRAKT_TRENDING_SHOWS_ERROR',
					details: {
						originalError:
							error instanceof Error ? error.message : 'Unknown error',
					},
				},
			},
			500
		);
	}
});

// Get popular TV shows
const getPopularShowsRoute = createRoute({
	method: 'get',
	path: '/shows/popular',
	tags: ['Trakt.tv'],
	summary: 'Get popular TV shows from Trakt.tv',
	description: 'Get the most popular TV shows on Trakt.tv',
	request: {
		query: TraktQueryParamsSchema,
	},
	responses: {
		200: {
			content: {
				'application/json': {
					schema: TraktSuccessResponseSchema.extend({
						data: TraktShowsResponseSchema,
					}),
				},
			},
			description: 'Popular TV shows retrieved successfully',
		},
		500: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'Internal server error',
		},
	},
});

app.openapi(getPopularShowsRoute, async c => {
	try {
		const { limit } = c.req.valid('query');

		const popularShows = await traktClient.getPopularShows(limit);

		return c.json({
			success: true,
			data: popularShows,
			message: 'Popular TV shows retrieved successfully',
		});
	} catch (error) {
		console.error('Get popular shows error:', error);

		return c.json(
			{
				success: false,
				error: {
					message: 'Failed to get popular TV shows',
					code: 'TRAKT_POPULAR_SHOWS_ERROR',
					details: {
						originalError:
							error instanceof Error ? error.message : 'Unknown error',
					},
				},
			},
			500
		);
	}
});

// Search movies and shows
const searchRoute = createRoute({
	method: 'get',
	path: '/search',
	tags: ['Trakt.tv'],
	summary: 'Search movies and TV shows on Trakt.tv',
	description: 'Search for movies and TV shows using Trakt.tv API',
	request: {
		query: TraktSearchQuerySchema,
	},
	responses: {
		200: {
			content: {
				'application/json': {
					schema: TraktSuccessResponseSchema.extend({
						data: TraktSearchResponseSchema,
					}),
				},
			},
			description: 'Search results retrieved successfully',
		},
		400: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'Bad request - invalid query parameters',
		},
		500: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'Internal server error',
		},
	},
});

app.openapi(searchRoute, async c => {
	try {
		const { query, type, limit } = c.req.valid('query');

		const searchResults = await traktClient.search(query, type, limit);

		return c.json({
			success: true,
			data: searchResults,
			message: 'Search completed successfully',
		});
	} catch (error) {
		console.error('Search error:', error);

		return c.json(
			{
				success: false,
				error: {
					message: 'Failed to search content',
					code: 'TRAKT_SEARCH_ERROR',
					details: {
						originalError:
							error instanceof Error ? error.message : 'Unknown error',
					},
				},
			},
			500
		);
	}
});

// Get movie details
const getMovieRoute = createRoute({
	method: 'get',
	path: '/movies/{id}',
	tags: ['Trakt.tv'],
	summary: 'Get movie details from Trakt.tv',
	description: 'Get detailed information about a specific movie',
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		200: {
			content: {
				'application/json': {
					schema: TraktSuccessResponseSchema.extend({
						data: z.any(), // TraktMovieSchema with extended fields
					}),
				},
			},
			description: 'Movie details retrieved successfully',
		},
		404: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'Movie not found',
		},
		500: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'Internal server error',
		},
	},
});

app.openapi(getMovieRoute, async c => {
	try {
		const { id } = c.req.valid('param');

		const movie = await traktClient.getMovie(id);

		return c.json({
			success: true,
			data: movie,
			message: 'Movie details retrieved successfully',
		});
	} catch (error) {
		console.error('Get movie error:', error);

		if (error instanceof Error && error.message.includes('404')) {
			return c.json(
				{
					success: false,
					error: {
						message: 'Movie not found',
						code: 'TRAKT_MOVIE_NOT_FOUND',
					},
				},
				404
			);
		}

		return c.json(
			{
				success: false,
				error: {
					message: 'Failed to get movie details',
					code: 'TRAKT_MOVIE_ERROR',
					details: {
						originalError:
							error instanceof Error ? error.message : 'Unknown error',
					},
				},
			},
			500
		);
	}
});

// Get show details
const getShowRoute = createRoute({
	method: 'get',
	path: '/shows/{id}',
	tags: ['Trakt.tv'],
	summary: 'Get TV show details from Trakt.tv',
	description: 'Get detailed information about a specific TV show',
	request: {
		params: z.object({
			id: z.string(),
		}),
	},
	responses: {
		200: {
			content: {
				'application/json': {
					schema: TraktSuccessResponseSchema.extend({
						data: z.any(), // TraktShowSchema with extended fields
					}),
				},
			},
			description: 'TV show details retrieved successfully',
		},
		404: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'TV show not found',
		},
		500: {
			content: {
				'application/json': {
					schema: TraktErrorResponseSchema,
				},
			},
			description: 'Internal server error',
		},
	},
});

app.openapi(getShowRoute, async c => {
	try {
		const { id } = c.req.valid('param');

		const show = await traktClient.getShow(id);

		return c.json({
			success: true,
			data: show,
			message: 'TV show details retrieved successfully',
		});
	} catch (error) {
		console.error('Get show error:', error);

		if (error instanceof Error && error.message.includes('404')) {
			return c.json(
				{
					success: false,
					error: {
						message: 'TV show not found',
						code: 'TRAKT_SHOW_NOT_FOUND',
					},
				},
				404
			);
		}

		return c.json(
			{
				success: false,
				error: {
					message: 'Failed to get TV show details',
					code: 'TRAKT_SHOW_ERROR',
					details: {
						originalError:
							error instanceof Error ? error.message : 'Unknown error',
					},
				},
			},
			500
		);
	}
});

export default app;

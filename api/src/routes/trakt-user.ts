import { createRoute, OpenAPIHono, z } from '@hono/zod-openapi';
import { HTTPException } from 'hono/http-exception';
import { traktClient } from '@/lib/trakt';
import { authMiddleware, getCurrentUser } from '@/middleware/auth';
import {
  TraktWatchedResponseSchema,
  TraktHistoryResponseSchema,
  TraktWatchlistResponseSchema,
  TraktStatsSchema,
  TraktUserSchema,
  TraktHistoryQuerySchema,
  TraktWatchlistQuerySchema,
  TraktErrorResponseSchema,
  TraktSuccessResponseSchema,
} from '@/schemas/trakt';

const app = new OpenAPIHono();

// Apply auth middleware to all routes (these require authentication)
app.use('*', authMiddleware);

// Get user profile
const getUserProfileRoute = createRoute({
  method: 'get',
  path: '/users/{username}',
  tags: ['Trakt.tv User'],
  summary: 'Get user profile from Trakt.tv',
  description: 'Get public profile information for a Trakt.tv user',
  security: [{ bearerAuth: [] }],
  request: {
    params: z.object({
      username: z.string(),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: TraktSuccessResponseSchema.extend({
            data: TraktUserSchema,
          }),
        },
      },
      description: 'User profile retrieved successfully',
    },
    404: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'User not found',
    },
    401: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'Unauthorized',
    },
    500: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'Internal server error',
    },
  },
});

app.openapi(getUserProfileRoute, async (c) => {
  try {
    const { username } = c.req.valid('param');
    
    const userProfile = await traktClient.getUserProfile(username);
    
    return c.json({
      success: true,
      data: userProfile,
      message: 'User profile retrieved successfully',
    });
  } catch (error) {
    console.error('Get user profile error:', error);
    
    if (error instanceof Error && error.message.includes('404')) {
      return c.json({
        success: false,
        error: {
          message: 'User not found',
          code: 'TRAKT_USER_NOT_FOUND',
        },
      }, 404);
    }
    
    return c.json({
      success: false,
      error: {
        message: 'Failed to get user profile',
        code: 'TRAKT_USER_PROFILE_ERROR',
        details: { originalError: error instanceof Error ? error.message : 'Unknown error' },
      },
    }, 500);
  }
});

// Get user statistics
const getUserStatsRoute = createRoute({
  method: 'get',
  path: '/users/{username}/stats',
  tags: ['Trakt.tv User'],
  summary: 'Get user statistics from Trakt.tv',
  description: 'Get viewing statistics for a Trakt.tv user',
  security: [{ bearerAuth: [] }],
  request: {
    params: z.object({
      username: z.string(),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: TraktSuccessResponseSchema.extend({
            data: TraktStatsSchema,
          }),
        },
      },
      description: 'User statistics retrieved successfully',
    },
    404: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'User not found',
    },
    401: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'Unauthorized',
    },
    500: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'Internal server error',
    },
  },
});

app.openapi(getUserStatsRoute, async (c) => {
  try {
    const { username } = c.req.valid('param');
    
    const userStats = await traktClient.getUserStats(username);
    
    return c.json({
      success: true,
      data: userStats,
      message: 'User statistics retrieved successfully',
    });
  } catch (error) {
    console.error('Get user stats error:', error);
    
    if (error instanceof Error && error.message.includes('404')) {
      return c.json({
        success: false,
        error: {
          message: 'User not found',
          code: 'TRAKT_USER_NOT_FOUND',
        },
      }, 404);
    }
    
    return c.json({
      success: false,
      error: {
        message: 'Failed to get user statistics',
        code: 'TRAKT_USER_STATS_ERROR',
        details: { originalError: error instanceof Error ? error.message : 'Unknown error' },
      },
    }, 500);
  }
});

// Get user watch history
const getUserHistoryRoute = createRoute({
  method: 'get',
  path: '/users/{username}/history',
  tags: ['Trakt.tv User'],
  summary: 'Get user watch history from Trakt.tv',
  description: 'Get the watch history for a Trakt.tv user',
  security: [{ bearerAuth: [] }],
  request: {
    params: z.object({
      username: z.string(),
    }),
    query: TraktHistoryQuerySchema,
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: TraktSuccessResponseSchema.extend({
            data: TraktHistoryResponseSchema,
          }),
        },
      },
      description: 'User history retrieved successfully',
    },
    404: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'User not found',
    },
    401: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'Unauthorized',
    },
    500: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'Internal server error',
    },
  },
});

app.openapi(getUserHistoryRoute, async (c) => {
  try {
    const { username } = c.req.valid('param');
    const { type, limit } = c.req.valid('query');
    
    const userHistory = await traktClient.getUserHistory(username, type, limit);
    
    return c.json({
      success: true,
      data: userHistory,
      message: 'User history retrieved successfully',
    });
  } catch (error) {
    console.error('Get user history error:', error);
    
    if (error instanceof Error && error.message.includes('404')) {
      return c.json({
        success: false,
        error: {
          message: 'User not found',
          code: 'TRAKT_USER_NOT_FOUND',
        },
      }, 404);
    }
    
    return c.json({
      success: false,
      error: {
        message: 'Failed to get user history',
        code: 'TRAKT_USER_HISTORY_ERROR',
        details: { originalError: error instanceof Error ? error.message : 'Unknown error' },
      },
    }, 500);
  }
});

// Get user watched movies
const getUserWatchedMoviesRoute = createRoute({
  method: 'get',
  path: '/users/{username}/watched/movies',
  tags: ['Trakt.tv User'],
  summary: 'Get user watched movies from Trakt.tv',
  description: 'Get all movies watched by a Trakt.tv user',
  security: [{ bearerAuth: [] }],
  request: {
    params: z.object({
      username: z.string(),
    }),
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: TraktSuccessResponseSchema.extend({
            data: TraktWatchedResponseSchema,
          }),
        },
      },
      description: 'User watched movies retrieved successfully',
    },
    404: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'User not found',
    },
    401: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'Unauthorized',
    },
    500: {
      content: {
        'application/json': {
          schema: TraktErrorResponseSchema,
        },
      },
      description: 'Internal server error',
    },
  },
});

app.openapi(getUserWatchedMoviesRoute, async (c) => {
  try {
    const { username } = c.req.valid('param');
    
    const watchedMovies = await traktClient.getUserWatchedMovies(username);
    
    return c.json({
      success: true,
      data: watchedMovies,
      message: 'User watched movies retrieved successfully',
    });
  } catch (error) {
    console.error('Get user watched movies error:', error);
    
    if (error instanceof Error && error.message.includes('404')) {
      return c.json({
        success: false,
        error: {
          message: 'User not found',
          code: 'TRAKT_USER_NOT_FOUND',
        },
      }, 404);
    }
    
    return c.json({
      success: false,
      error: {
        message: 'Failed to get user watched movies',
        code: 'TRAKT_USER_WATCHED_MOVIES_ERROR',
        details: { originalError: error instanceof Error ? error.message : 'Unknown error' },
      },
    }, 500);
  }
});

export default app;

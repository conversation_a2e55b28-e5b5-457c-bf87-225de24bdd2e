import { serve } from '@hono/node-server';
import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import { OpenAPIHono } from '@hono/zod-openapi';
import { apiReference } from '@scalar/hono-api-reference';

import { env } from '@/lib/env';
import { notFound, onError, serveEmojiFavicon } from '@/lib/configure-open-api';

// Import route modules
import healthRoutes from '@/routes/health';
import tmdbRoutes from '@/routes/tmdb';
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import mediaRoutes from '@/routes/media';
import watchlistRoutes from '@/routes/watchlists';

// Create the main app
const app = new OpenAPIHono({
	strict: false,
	defaultHook: (result, c) => {
		if (!result.success) {
			return c.json(
				{
					success: false,
					error: {
						issues: result.error.issues,
					},
				},
				422
			);
		}
	},
});

// Global middleware
app.use(logger());
app.use(prettyJSON());
app.use(secureHeaders());

// CORS configuration
app.use(
	'*',
	cors({
		origin: env.CORS_ORIGIN.split(','),
		allowHeaders: ['Content-Type', 'Authorization'],
		allowMethods: ['GET', 'POST', 'PATCH', 'DELETE', 'OPTIONS'],
		exposeHeaders: ['Content-Length'],
		maxAge: 600,
		credentials: true,
	})
);

// Favicon
app.get('/favicon.ico', serveEmojiFavicon('🎬'));

// API Documentation
app.doc('/doc', {
	openapi: '3.0.0',
	info: {
		version: '1.0.0',
		title: 'Streama API',
		description:
			'Media discovery and analytics API powered by TMDB and Trakt.tv',
		contact: {
			name: 'Streama Team',
			email: '<EMAIL>',
		},
		license: {
			name: 'MIT',
			url: 'https://opensource.org/licenses/MIT',
		},
	},
	servers: [
		{
			url: `http://localhost:${env.PORT}`,
			description: 'Development server',
		},
	],
	tags: [
		{
			name: 'Health',
			description: 'Health check endpoints',
		},
		{
			name: 'Authentication',
			description: 'User authentication endpoints',
		},
		{
			name: 'Users',
			description: 'User management and profile endpoints',
		},
		{
			name: 'TMDB',
			description: 'The Movie Database integration endpoints',
		},
		{
			name: 'Media',
			description: 'Media management and caching endpoints',
		},
		{
			name: 'Watchlists',
			description: 'User watchlist management endpoints',
		},
		{
			name: 'Analytics',
			description: 'Analytics and tracking endpoints',
		},
	],
});

// Scalar API Reference
app.get(
	'/reference',
	apiReference({
		theme: 'kepler',
		layout: 'modern',
		spec: {
			url: '/doc',
		},
	})
);

// Routes
const routes = app
	.route('/health', healthRoutes)
	.route('/api/v1/auth', authRoutes)
	.route('/api/v1/users', userRoutes)
	.route('/api/v1/media', mediaRoutes)
	.route('/api/v1/watchlists', watchlistRoutes)
	.route('/api/v1/tmdb', tmdbRoutes);

// Error handling
app.onError(onError);
app.notFound(notFound);

// Export the app type for type safety
export type AppType = typeof routes;

// Start the server
const port = env.PORT;
console.log(`🎬 Streama API is running on port ${port}`);
console.log(`📚 API Documentation: http://localhost:${port}/reference`);
console.log(`🔍 OpenAPI Spec: http://localhost:${port}/doc`);

Bun.serve({
	fetch: app.fetch,
	port,
});

export default app;

import { cors } from 'hono/cors';
import { logger } from 'hono/logger';
import { prettyJSON } from 'hono/pretty-json';
import { secureHeaders } from 'hono/secure-headers';
import { OpenAPIHono } from '@hono/zod-openapi';
import { apiReference } from '@scalar/hono-api-reference';

import { env } from '@/lib/env';
import { notFound, onError, serveEmojiFavicon } from '@/lib/configure-open-api';

import healthRoutes from '@/routes/health';
import tmdbRoutes from '@/routes/tmdb';
import traktRoutes from '@/routes/trakt';
import traktUserRoutes from '@/routes/trakt-user';
import analyticsRoutes from '@/routes/analytics';
import authRoutes from '@/routes/auth';
import userRoutes from '@/routes/users';
import mediaRoutes from '@/routes/media';
import watchlistRoutes from '@/routes/watchlists';

const app = new OpenAPIHono({
	strict: false,
	defaultHook: (result, c) => {
		if (!result.success) {
			return c.json(
				{
					success: false,
					error: {
						issues: result.error.issues,
					},
				},
				422
			);
		}
	},
});

app.use(logger());
app.use(prettyJSON());
app.use(secureHeaders());

app.use(
	'*',
	cors({
		origin: env.CORS_ORIGIN.split(','),
		allowHeaders: ['Content-Type', 'Authorization'],
		allowMethods: ['GET', 'POST', 'PATCH', 'DELETE', 'OPTIONS'],
		exposeHeaders: ['Content-Length'],
		maxAge: 600,
		credentials: true,
	})
);

app.get('/favicon.ico', serveEmojiFavicon('🎬'));

app.doc('/doc', {
	openapi: '3.0.0',
	info: {
		version: '1.0.0',
		title: 'Streama API',
		description:
			'Media discovery and analytics API powered by TMDB and Trakt.tv',
		contact: {
			name: 'Streama Team',
			email: '<EMAIL>',
		},
		license: {
			name: 'MIT',
			url: 'https://opensource.org/licenses/MIT',
		},
	},
	servers: [
		{
			url: `http://localhost:${env.PORT}`,
			description: 'Development server',
		},
	],
	tags: [
		{
			name: 'Health',
			description: 'Health check endpoints',
		},
		{
			name: 'Authentication',
			description: 'User authentication endpoints',
		},
		{
			name: 'Users',
			description: 'User management and profile endpoints',
		},
		{
			name: 'TMDB',
			description: 'The Movie Database integration endpoints',
		},
		{
			name: 'Trakt.tv',
			description: 'Trakt.tv API integration endpoints',
		},
		{
			name: 'Trakt.tv User',
			description: 'Trakt.tv user-specific endpoints (requires authentication)',
		},
		{
			name: 'Media',
			description: 'Media management and caching endpoints',
		},
		{
			name: 'Watchlists',
			description: 'User watchlist management endpoints',
		},
		{
			name: 'Analytics',
			description: 'Analytics and tracking endpoints',
		},
	],
});

app.get(
	'/reference',
	apiReference({
		theme: 'kepler',
		layout: 'modern',
		spec: {
			url: '/docs',
		},
	})
);

const routes = app
	.basePath('/api/v1')
	.route('/health', healthRoutes)
	.route('/auth', authRoutes)
	.route('/users', userRoutes)
	.route('/media', mediaRoutes)
	.route('/watchlists', watchlistRoutes)
	.route('/tmdb', tmdbRoutes)
	.route('/trakt', traktRoutes)
	.route('/trakt', traktUserRoutes)
	.route('/analytics', analyticsRoutes);

app.onError(onError);
app.notFound(notFound);

export type AppType = typeof routes;

const port = env.PORT;
console.log(`🎬 Streama API is running on port ${port}`);
console.log(`📚 API Documentation: http://localhost:${port}/reference`);
console.log(`🔍 OpenAPI Spec: http://localhost:${port}/docs`);

Bun.serve({
	fetch: app.fetch,
	port,
});

export default app;

import { z } from 'zod';

// Base Trakt ID schema
export const TraktIdsSchema = z.object({
  trakt: z.number(),
  slug: z.string(),
  imdb: z.string().optional(),
  tmdb: z.number().optional(),
  tvdb: z.number().optional(),
});

// Trakt Movie schema
export const TraktMovieSchema = z.object({
  title: z.string(),
  year: z.number(),
  ids: TraktIdsSchema,
  tagline: z.string().optional(),
  overview: z.string().optional(),
  released: z.string().optional(),
  runtime: z.number().optional(),
  country: z.string().optional(),
  trailer: z.string().optional(),
  homepage: z.string().optional(),
  status: z.string().optional(),
  rating: z.number().optional(),
  votes: z.number().optional(),
  comment_count: z.number().optional(),
  language: z.string().optional(),
  available_translations: z.array(z.string()).optional(),
  genres: z.array(z.string()).optional(),
  certification: z.string().optional(),
});

// Trakt Show schema
export const TraktShowSchema = z.object({
  title: z.string(),
  year: z.number(),
  ids: TraktIdsSchema,
  overview: z.string().optional(),
  first_aired: z.string().optional(),
  airs: z.object({
    day: z.string().optional(),
    time: z.string().optional(),
    timezone: z.string().optional(),
  }).optional(),
  runtime: z.number().optional(),
  certification: z.string().optional(),
  network: z.string().optional(),
  country: z.string().optional(),
  trailer: z.string().optional(),
  homepage: z.string().optional(),
  status: z.string().optional(),
  rating: z.number().optional(),
  votes: z.number().optional(),
  comment_count: z.number().optional(),
  language: z.string().optional(),
  available_translations: z.array(z.string()).optional(),
  genres: z.array(z.string()).optional(),
  aired_episodes: z.number().optional(),
});

// Trakt Episode schema
export const TraktEpisodeSchema = z.object({
  season: z.number(),
  number: z.number(),
  title: z.string(),
  ids: TraktIdsSchema,
  number_abs: z.number().optional(),
  overview: z.string().optional(),
  first_aired: z.string().optional(),
  updated_at: z.string().optional(),
  available_translations: z.array(z.string()).optional(),
  runtime: z.number().optional(),
  rating: z.number().optional(),
  votes: z.number().optional(),
  comment_count: z.number().optional(),
});

// Trakt Watched Item schema
export const TraktWatchedItemSchema = z.object({
  last_watched_at: z.string(),
  last_updated_at: z.string(),
  plays: z.number(),
  reset_at: z.string().nullable(),
  movie: TraktMovieSchema.optional(),
  show: TraktShowSchema.optional(),
  seasons: z.array(z.object({
    number: z.number(),
    episodes: z.array(z.object({
      number: z.number(),
      plays: z.number(),
      last_watched_at: z.string(),
    })),
  })).optional(),
});

// Trakt History Item schema
export const TraktHistoryItemSchema = z.object({
  id: z.number(),
  watched_at: z.string(),
  action: z.enum(['scrobble', 'checkin', 'watch']),
  type: z.enum(['movie', 'episode']),
  movie: TraktMovieSchema.optional(),
  show: TraktShowSchema.optional(),
  episode: TraktEpisodeSchema.optional(),
});

// Trakt Stats schema
export const TraktStatsSchema = z.object({
  movies: z.object({
    plays: z.number(),
    watched: z.number(),
    minutes: z.number(),
    collected: z.number(),
    ratings: z.number(),
    comments: z.number(),
  }),
  shows: z.object({
    watched: z.number(),
    collected: z.number(),
    ratings: z.number(),
    comments: z.number(),
  }),
  seasons: z.object({
    ratings: z.number(),
    comments: z.number(),
  }),
  episodes: z.object({
    plays: z.number(),
    watched: z.number(),
    minutes: z.number(),
    collected: z.number(),
    ratings: z.number(),
    comments: z.number(),
  }),
  network: z.object({
    friends: z.number(),
    followers: z.number(),
    following: z.number(),
  }),
  ratings: z.object({
    total: z.number(),
    distribution: z.record(z.number()),
  }),
});

// Trakt Watchlist Item schema
export const TraktWatchlistItemSchema = z.object({
  rank: z.number(),
  id: z.number(),
  listed_at: z.string(),
  notes: z.string().nullable(),
  type: z.enum(['movie', 'show', 'season', 'episode']),
  movie: TraktMovieSchema.optional(),
  show: TraktShowSchema.optional(),
  episode: TraktEpisodeSchema.optional(),
});

// Trakt User schema
export const TraktUserSchema = z.object({
  username: z.string(),
  private: z.boolean(),
  name: z.string(),
  description: z.string().optional(),
  vip: z.boolean(),
  vip_ep: z.boolean(),
  ids: z.object({
    slug: z.string(),
  }),
  joined_at: z.string(),
  location: z.string().optional(),
  about: z.string().optional(),
  gender: z.string().optional(),
  age: z.number().optional(),
  images: z.object({
    avatar: z.object({
      full: z.string(),
    }),
  }).optional(),
});

// Trending item schemas
export const TraktTrendingMovieSchema = z.object({
  watchers: z.number(),
  movie: TraktMovieSchema,
});

export const TraktTrendingShowSchema = z.object({
  watchers: z.number(),
  show: TraktShowSchema,
});

// Search result schema
export const TraktSearchResultSchema = z.object({
  type: z.string(),
  score: z.number(),
  movie: TraktMovieSchema.optional(),
  show: TraktShowSchema.optional(),
});

// Response schemas for API endpoints
export const TraktMoviesResponseSchema = z.array(TraktMovieSchema);
export const TraktShowsResponseSchema = z.array(TraktShowSchema);
export const TraktTrendingMoviesResponseSchema = z.array(TraktTrendingMovieSchema);
export const TraktTrendingShowsResponseSchema = z.array(TraktTrendingShowSchema);
export const TraktWatchedResponseSchema = z.array(TraktWatchedItemSchema);
export const TraktHistoryResponseSchema = z.array(TraktHistoryItemSchema);
export const TraktWatchlistResponseSchema = z.array(TraktWatchlistItemSchema);
export const TraktSearchResponseSchema = z.array(TraktSearchResultSchema);

// Query parameter schemas
export const TraktQueryParamsSchema = z.object({
  limit: z.coerce.number().min(1).max(100).default(20),
  page: z.coerce.number().min(1).default(1),
});

export const TraktHistoryQuerySchema = TraktQueryParamsSchema.extend({
  type: z.enum(['movies', 'shows']).optional(),
  start_at: z.string().optional(),
  end_at: z.string().optional(),
});

export const TraktSearchQuerySchema = TraktQueryParamsSchema.extend({
  query: z.string().min(1),
  type: z.enum(['movie', 'show']).optional(),
});

export const TraktWatchlistQuerySchema = z.object({
  type: z.enum(['movies', 'shows']).optional(),
});

// Error response schema
export const TraktErrorResponseSchema = z.object({
  success: z.boolean().default(false),
  error: z.object({
    message: z.string(),
    code: z.string().optional(),
    details: z.record(z.any()).optional(),
  }),
});

// Success response schema
export const TraktSuccessResponseSchema = z.object({
  success: z.boolean().default(true),
  data: z.any(),
  message: z.string().optional(),
});

// Export types
export type TraktMovie = z.infer<typeof TraktMovieSchema>;
export type TraktShow = z.infer<typeof TraktShowSchema>;
export type TraktEpisode = z.infer<typeof TraktEpisodeSchema>;
export type TraktWatchedItem = z.infer<typeof TraktWatchedItemSchema>;
export type TraktHistoryItem = z.infer<typeof TraktHistoryItemSchema>;
export type TraktStats = z.infer<typeof TraktStatsSchema>;
export type TraktWatchlistItem = z.infer<typeof TraktWatchlistItemSchema>;
export type TraktUser = z.infer<typeof TraktUserSchema>;
export type TraktTrendingMovie = z.infer<typeof TraktTrendingMovieSchema>;
export type TraktTrendingShow = z.infer<typeof TraktTrendingShowSchema>;
export type TraktSearchResult = z.infer<typeof TraktSearchResultSchema>;
export type TraktQueryParams = z.infer<typeof TraktQueryParamsSchema>;
export type TraktHistoryQuery = z.infer<typeof TraktHistoryQuerySchema>;
export type TraktSearchQuery = z.infer<typeof TraktSearchQuerySchema>;
export type TraktWatchlistQuery = z.infer<typeof TraktWatchlistQuerySchema>;

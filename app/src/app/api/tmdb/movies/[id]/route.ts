import { NextRequest, NextResponse } from 'next/server';
import { tmdbClient } from '@/api/tmdb';
import { TMDBMovieDetails } from '@/types/tmdb';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const movieId = parseInt(params.id);

    if (isNaN(movieId) || movieId <= 0) {
      return NextResponse.json(
        { error: 'Invalid movie ID' },
        { status: 400 }
      );
    }

    const data: TMDBMovieDetails = await tmdbClient.getMovieDetails(movieId);

    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=604800',
      },
    });
  } catch (error) {
    console.error(`Error fetching movie details for ID ${params.id}:`, error);
    
    if (error instanceof Error && error.message.includes('404')) {
      return NextResponse.json(
        { error: 'Movie not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch movie details' },
      { status: 500 }
    );
  }
}

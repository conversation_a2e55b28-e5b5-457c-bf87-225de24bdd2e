import { NextRequest, NextResponse } from 'next/server';
import { tmdbClient } from '@/api/tmdb';
import { TMDBResponse, TMDBMultiSearchResult } from '@/types/tmdb';

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);
		const query = searchParams.get('query');
		const page = parseInt(searchParams.get('page') || '1');
		const type =
			(searchParams.get('type') as 'movie' | 'tv' | 'multi') || 'multi';

		if (!query || query.trim().length === 0) {
			return NextResponse.json(
				{ error: 'Query parameter is required' },
				{ status: 400 }
			);
		}

		if (query.length > 100) {
			return NextResponse.json(
				{ error: 'Query must be less than 100 characters' },
				{ status: 400 }
			);
		}

		if (page < 1 || page > 1000) {
			return NextResponse.json(
				{ error: 'Page must be between 1 and 1000' },
				{ status: 400 }
			);
		}

		if (!['movie', 'tv', 'multi'].includes(type)) {
			return NextResponse.json(
				{ error: 'Type must be "movie", "tv", or "multi"' },
				{ status: 400 }
			);
		}

		let data: TMDBResponse<any>;

		switch (type) {
			case 'movie':
				data = await tmdbClient.searchMovies(query, page);
				break;
			case 'tv':
				data = await tmdbClient.searchTVShows(query, page);
				break;
			case 'multi':
			default:
				data = await tmdbClient.searchMulti(query, page);
				break;
		}

		return NextResponse.json(data, {
			headers: {
				'Cache-Control': 'public, s-maxage=1800, stale-while-revalidate=3600',
			},
		});
	} catch (error) {
		console.error('Error performing search:', error);
		return NextResponse.json(
			{ error: 'Failed to perform search' },
			{ status: 500 }
		);
	}
}

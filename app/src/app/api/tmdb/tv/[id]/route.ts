import { NextRequest, NextResponse } from 'next/server';
import { tmdbClient } from '@/api/tmdb';
import { TMDBTVShowDetails } from '@/types/tmdb';

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const tvId = parseInt(params.id);

    if (isNaN(tvId) || tvId <= 0) {
      return NextResponse.json(
        { error: 'Invalid TV show ID' },
        { status: 400 }
      );
    }

    const data: TMDBTVShowDetails = await tmdbClient.getTVShowDetails(tvId);

    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=604800',
      },
    });
  } catch (error) {
    console.error(`Error fetching TV show details for ID ${params.id}:`, error);
    
    if (error instanceof Error && error.message.includes('404')) {
      return NextResponse.json(
        { error: 'TV show not found' },
        { status: 404 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to fetch TV show details' },
      { status: 500 }
    );
  }
}

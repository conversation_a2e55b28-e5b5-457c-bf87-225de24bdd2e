import { NextRequest, NextResponse } from 'next/server';
import { tmdbClient } from '@/api/tmdb';
import { TMDBGenresResponse } from '@/types/tmdb';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const type = searchParams.get('type') as 'movie' | 'tv' || 'movie';

    if (!['movie', 'tv'].includes(type)) {
      return NextResponse.json(
        { error: 'Type must be "movie" or "tv"' },
        { status: 400 }
      );
    }

    let data: TMDBGenresResponse;

    if (type === 'movie') {
      data = await tmdbClient.getMovieGenres();
    } else {
      data = await tmdbClient.getTVGenres();
    }

    return NextResponse.json(data, {
      headers: {
        'Cache-Control': 'public, s-maxage=86400, stale-while-revalidate=604800',
      },
    });
  } catch (error) {
    console.error('Error fetching genres:', error);
    return NextResponse.json(
      { error: 'Failed to fetch genres' },
      { status: 500 }
    );
  }
}

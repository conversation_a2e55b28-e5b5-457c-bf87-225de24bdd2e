import { NextResponse } from 'next/server';

export async function GET() {
  try {
    const apiKey = process.env.TMDB_API_KEY;
    
    console.log('Test TMDB API:', {
      hasApiKey: !!apiKey,
      apiKeyLength: apiKey?.length || 0,
      apiKeyStart: apiKey?.substring(0, 20) + '...',
    });

    if (!apiKey) {
      return NextResponse.json(
        { error: 'TMDB API key not found' },
        { status: 500 }
      );
    }

    const url = 'https://api.themoviedb.org/3/movie/popular?language=en-US&page=1';
    
    console.log('Making request to:', url);

    const response = await fetch(url, {
      headers: {
        'Accept': 'application/json',
        'Authorization': `Bearer ${apiKey}`,
      },
    });

    console.log('Response status:', response.status, response.statusText);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('TMDB API Error:', errorText);
      return NextResponse.json(
        { 
          error: 'TMDB API Error',
          status: response.status,
          statusText: response.statusText,
          details: errorText
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    return NextResponse.json({
      success: true,
      totalResults: data.total_results,
      resultsCount: data.results?.length || 0,
      firstMovie: data.results?.[0]?.title || 'No movies found',
    });

  } catch (error) {
    console.error('Test TMDB API failed:', error);
    return NextResponse.json(
      { 
        error: 'Failed to test TMDB API',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

'use client';

import { useState, useEffect } from 'react';
import { MediaGrid } from '@/components/media-card';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { apiClient } from '@/lib/api';
import type { TMDBMovie, TMDBTVShow, TMDBGenre } from '@/types/api';

export default function DiscoverPage() {
  const [activeTab, setActiveTab] = useState<'movie' | 'tv'>('movie');
  const [popularMovies, setPopularMovies] = useState<TMDBMovie[]>([]);
  const [popularTVShows, setPopularTVShows] = useState<TMDBTVShow[]>([]);
  const [trendingMovies, setTrendingMovies] = useState<TMDBMovie[]>([]);
  const [trendingTVShows, setTrendingTVShows] = useState<TMDBTVShow[]>([]);
  const [genres, setGenres] = useState<TMDBGenre[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadContent();
  }, []);

  const loadContent = async () => {
    try {
      setLoading(true);
      setError(null);

      const [
        popularMoviesRes,
        popularTVRes,
        trendingMoviesRes,
        trendingTVRes,
        genresRes,
      ] = await Promise.all([
        apiClient.getPopularMovies(),
        apiClient.getPopularTVShows(),
        apiClient.getTrendingMovies(),
        apiClient.getTrendingTVShows(),
        apiClient.getGenres('movie'),
      ]);

      setPopularMovies(popularMoviesRes.results.slice(0, 20));
      setPopularTVShows(popularTVRes.results.slice(0, 20));
      setTrendingMovies(trendingMoviesRes.results.slice(0, 20));
      setTrendingTVShows(trendingTVRes.results.slice(0, 20));
      setGenres(genresRes.genres.slice(0, 12));
    } catch (err) {
      console.error('Error loading content:', err);
      setError('Failed to load content. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className='min-h-screen bg-gray-50'>
        <div className='container mx-auto px-4 py-8'>
          <div className='flex items-center justify-center py-20'>
            <div className='text-center'>
              <div className='animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4'></div>
              <p className='text-gray-600'>Loading amazing content...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className='min-h-screen bg-gray-50'>
        <div className='container mx-auto px-4 py-8'>
          <div className='flex items-center justify-center py-20'>
            <Card className='max-w-md'>
              <CardHeader>
                <CardTitle className='text-center text-red-600'>Error</CardTitle>
              </CardHeader>
              <CardContent className='text-center space-y-4'>
                <p className='text-gray-600'>{error}</p>
                <Button onClick={loadContent}>Try Again</Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className='min-h-screen bg-gray-50'>
      <div className='container mx-auto px-4 py-8'>
        {/* Header */}
        <div className='mb-8'>
          <h1 className='text-3xl font-bold text-gray-900 mb-4'>Discover</h1>
          <p className='text-gray-600'>
            Explore trending and popular movies and TV shows
          </p>
        </div>

        {/* Tab Navigation */}
        <div className='flex space-x-1 mb-8 bg-gray-200 p-1 rounded-lg w-fit'>
          <button
            onClick={() => setActiveTab('movie')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'movie'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            Movies
          </button>
          <button
            onClick={() => setActiveTab('tv')}
            className={`px-6 py-2 rounded-md text-sm font-medium transition-colors ${
              activeTab === 'tv'
                ? 'bg-white text-gray-900 shadow-sm'
                : 'text-gray-600 hover:text-gray-900'
            }`}
          >
            TV Shows
          </button>
        </div>

        {/* Content Sections */}
        <div className='space-y-12'>
          {activeTab === 'movie' ? (
            <>
              {/* Trending Movies */}
              <section>
                <h2 className='text-2xl font-bold text-gray-900 mb-6'>
                  Trending Movies
                </h2>
                <MediaGrid media={trendingMovies} type='movie' />
              </section>

              {/* Popular Movies */}
              <section>
                <h2 className='text-2xl font-bold text-gray-900 mb-6'>
                  Popular Movies
                </h2>
                <MediaGrid media={popularMovies} type='movie' />
              </section>
            </>
          ) : (
            <>
              {/* Trending TV Shows */}
              <section>
                <h2 className='text-2xl font-bold text-gray-900 mb-6'>
                  Trending TV Shows
                </h2>
                <MediaGrid media={trendingTVShows} type='tv' />
              </section>

              {/* Popular TV Shows */}
              <section>
                <h2 className='text-2xl font-bold text-gray-900 mb-6'>
                  Popular TV Shows
                </h2>
                <MediaGrid media={popularTVShows} type='tv' />
              </section>
            </>
          )}

          {/* Genres */}
          {genres.length > 0 && (
            <section>
              <h2 className='text-2xl font-bold text-gray-900 mb-6'>
                Browse by Genre
              </h2>
              <div className='grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4'>
                {genres.map((genre) => (
                  <Card
                    key={genre.id}
                    className='cursor-pointer hover:shadow-md transition-shadow'
                  >
                    <CardContent className='p-4 text-center'>
                      <p className='font-medium text-sm'>{genre.name}</p>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </section>
          )}
        </div>
      </div>
    </div>
  );
}

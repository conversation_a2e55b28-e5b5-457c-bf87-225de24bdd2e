import Link from 'next/link';
import { Button } from '@/components/ui/button';
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from '@/components/ui/card';

export default function Home() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<div className='container mx-auto px-4 py-8'>
				{/* Hero Section */}
				<section className='text-center space-y-6 py-12'>
					<h1 className='text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl'>
						Welcome to Streama
					</h1>
					<p className='text-xl text-gray-600 max-w-2xl mx-auto'>
						Discover movies and TV shows with comprehensive analytics powered by
						TMDB. Track your favorites, create watchlists, and get personalized
						recommendations.
					</p>
					<div className='flex gap-4 justify-center'>
						<Link href='/discover'>
							<Button size='lg'>Get Started</Button>
						</Link>
						<Link href='/about'>
							<Button variant='outline' size='lg'>
								Learn More
							</Button>
						</Link>
					</div>
				</section>

				{/* Features Section */}
				<section className='py-12'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Features
					</h2>
					<div className='grid md:grid-cols-3 gap-8'>
						<Card>
							<CardHeader className='text-center'>
								<div className='w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4'>
									<span className='text-2xl'>🎬</span>
								</div>
								<CardTitle>Discover Content</CardTitle>
								<CardDescription>
									Explore trending movies and TV shows with detailed information
									from TMDB.
								</CardDescription>
							</CardHeader>
						</Card>

						<Card>
							<CardHeader className='text-center'>
								<div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4'>
									<span className='text-2xl'>📝</span>
								</div>
								<CardTitle>Create Watchlists</CardTitle>
								<CardDescription>
									Organize your favorite content into custom watchlists and
									track your viewing progress.
								</CardDescription>
							</CardHeader>
						</Card>

						<Card>
							<CardHeader className='text-center'>
								<div className='w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4'>
									<span className='text-2xl'>📊</span>
								</div>
								<CardTitle>Analytics & Insights</CardTitle>
								<CardDescription>
									Get detailed analytics about your viewing habits and
									personalized recommendations.
								</CardDescription>
							</CardHeader>
						</Card>
					</div>
				</section>
			</div>
		</div>
	);
}

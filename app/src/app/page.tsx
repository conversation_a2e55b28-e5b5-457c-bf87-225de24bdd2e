import { Suspense } from 'react';
import { MediaSection } from '@/components/media-section';
import { GenreCard } from '@/components/genre-card';
import {
	MediaSectionSkeleton,
	GenreGridSkeleton,
} from '@/components/loading-skeletons';
import { ErrorBoundary } from '@/components/error-boundary';
import {
	TMDBResponse,
	TMDBMovie,
	TMDBTVShow,
	TMDBGenresResponse,
} from '@/types/tmdb';

async function getPopularMovies(): Promise<TMDBMovie[]> {
	try {
		const response = await fetch(
			`${
				process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
			}/api/tmdb/movies/popular`,
			{
				next: { revalidate: 3600 },
			}
		);

		if (!response.ok) {
			throw new Error('Failed to fetch popular movies');
		}

		const data: TMDBResponse<TMDBMovie> = await response.json();
		return data.results.slice(0, 20); // Limit to 20 items for horizontal scroll
	} catch (error) {
		console.error('Error fetching popular movies:', error);
		return [];
	}
}

async function getPopularTVShows(): Promise<TMDBTVShow[]> {
	try {
		const response = await fetch(
			`${
				process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
			}/api/tmdb/tv/popular`,
			{
				next: { revalidate: 3600 },
			}
		);

		if (!response.ok) {
			throw new Error('Failed to fetch popular TV shows');
		}

		const data: TMDBResponse<TMDBTVShow> = await response.json();
		return data.results.slice(0, 20); // Limit to 20 items for horizontal scroll
	} catch (error) {
		console.error('Error fetching popular TV shows:', error);
		return [];
	}
}

async function getTrendingMovies(): Promise<TMDBMovie[]> {
	try {
		const response = await fetch(
			`${
				process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
			}/api/tmdb/movies/trending`,
			{
				next: { revalidate: 1800 },
			}
		);

		if (!response.ok) {
			throw new Error('Failed to fetch trending movies');
		}

		const data: TMDBResponse<TMDBMovie> = await response.json();
		return data.results.slice(0, 20);
	} catch (error) {
		console.error('Error fetching trending movies:', error);
		return [];
	}
}

async function getMovieGenres() {
	try {
		const response = await fetch(
			`${
				process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'
			}/api/tmdb/genres?type=movie`,
			{
				next: { revalidate: 86400 },
			}
		);

		if (!response.ok) {
			throw new Error('Failed to fetch movie genres');
		}

		const data: TMDBGenresResponse = await response.json();
		return data.genres.slice(0, 12); // Limit to 12 genres
	} catch (error) {
		console.error('Error fetching movie genres:', error);
		return [];
	}
}

export default async function Home() {
	const [popularMovies, popularTVShows, trendingMovies, movieGenres] =
		await Promise.all([
			getPopularMovies(),
			getPopularTVShows(),
			getTrendingMovies(),
			getMovieGenres(),
		]);

	return (
		<div className='min-h-screen bg-background'>
			<div className='container mx-auto px-4 py-8 space-y-12'>
				{/* Hero Section */}
				<section className='text-center space-y-4'>
					<h1 className='text-4xl font-bold tracking-tight sm:text-6xl'>
						Discover Amazing Content
					</h1>
					<p className='text-xl text-muted-foreground max-w-2xl mx-auto'>
						Explore the latest movies and TV shows with comprehensive analytics
						and personalized recommendations.
					</p>
				</section>

				{/* Trending Movies */}
				<ErrorBoundary>
					<Suspense fallback={<MediaSectionSkeleton />}>
						<MediaSection
							title='Trending Movies'
							media={trendingMovies}
							showSeeAll
							seeAllHref='/discover/movie?sort_by=popularity.desc'
						/>
					</Suspense>
				</ErrorBoundary>

				{/* Popular Movies */}
				<ErrorBoundary>
					<Suspense fallback={<MediaSectionSkeleton />}>
						<MediaSection
							title='Popular Movies'
							media={popularMovies}
							showSeeAll
							seeAllHref='/discover/movie?sort_by=popularity.desc'
						/>
					</Suspense>
				</ErrorBoundary>

				{/* Popular TV Shows */}
				<ErrorBoundary>
					<Suspense fallback={<MediaSectionSkeleton />}>
						<MediaSection
							title='Popular TV Shows'
							media={popularTVShows}
							showSeeAll
							seeAllHref='/discover/tv?sort_by=popularity.desc'
						/>
					</Suspense>
				</ErrorBoundary>

				{/* Movie Genres */}
				<section className='space-y-6'>
					<div className='flex items-center justify-between'>
						<h2 className='text-2xl font-bold'>Browse by Genre</h2>
					</div>

					<ErrorBoundary>
						<Suspense fallback={<GenreGridSkeleton />}>
							<div className='grid gap-4 grid-cols-2 md:grid-cols-4 lg:grid-cols-6'>
								{movieGenres.map(genre => (
									<GenreCard key={genre.id} genre={genre} mediaType='movie' />
								))}
							</div>
						</Suspense>
					</ErrorBoundary>
				</section>
			</div>
		</div>
	);
}

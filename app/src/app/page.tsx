export default function Home() {
	return (
		<div className='min-h-screen bg-gray-50'>
			<div className='container mx-auto px-4 py-8'>
				{/* Hero Section */}
				<section className='text-center space-y-6 py-12'>
					<h1 className='text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl'>
						Welcome to Streama
					</h1>
					<p className='text-xl text-gray-600 max-w-2xl mx-auto'>
						Discover movies and TV shows with comprehensive analytics powered by
						TMDB. Track your favorites, create watchlists, and get personalized
						recommendations.
					</p>
					<div className='flex gap-4 justify-center'>
						<button className='bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors'>
							Get Started
						</button>
						<button className='border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors'>
							Learn More
						</button>
					</div>
				</section>

				{/* Features Section */}
				<section className='py-12'>
					<h2 className='text-3xl font-bold text-center text-gray-900 mb-12'>
						Features
					</h2>
					<div className='grid md:grid-cols-3 gap-8'>
						<div className='text-center space-y-4'>
							<div className='w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto'>
								<span className='text-2xl'>🎬</span>
							</div>
							<h3 className='text-xl font-semibold text-gray-900'>
								Discover Content
							</h3>
							<p className='text-gray-600'>
								Explore trending movies and TV shows with detailed information
								from TMDB.
							</p>
						</div>
						<div className='text-center space-y-4'>
							<div className='w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto'>
								<span className='text-2xl'>📝</span>
							</div>
							<h3 className='text-xl font-semibold text-gray-900'>
								Create Watchlists
							</h3>
							<p className='text-gray-600'>
								Organize your favorite content into custom watchlists and track
								your viewing progress.
							</p>
						</div>
						<div className='text-center space-y-4'>
							<div className='w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto'>
								<span className='text-2xl'>📊</span>
							</div>
							<h3 className='text-xl font-semibold text-gray-900'>
								Analytics & Insights
							</h3>
							<p className='text-gray-600'>
								Get detailed analytics about your viewing habits and
								personalized recommendations.
							</p>
						</div>
					</div>
				</section>
			</div>
		</div>
	);
}

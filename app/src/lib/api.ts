import type { 
  ApiResponse, 
  PaginatedResponse, 
  ApiError,
  AuthResponse,
  LoginForm,
  RegisterForm,
  User,
  UserUpdateForm,
  UserPreferencesForm,
  Media,
  Watchlist,
  WatchlistCreate,
  WatchlistItemCreate,
  Rating,
  RatingCreate,
  Review,
  ReviewCreate,
  Favorite,
  FavoriteCreate,
  ViewHistory,
  ViewHistoryCreate,
  SearchHistory,
  SearchHistoryCreate,
  TMDBResponse,
  TMDBMovie,
  TMDBTVShow,
  TMDBGenresResponse
} from '@/types/api';

// API Configuration
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3002';

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    // Try to get token from localStorage on client side
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }
  }

  setToken(token: string | null) {
    this.token = token;
    if (typeof window !== 'undefined') {
      if (token) {
        localStorage.setItem('auth_token', token);
      } else {
        localStorage.removeItem('auth_token');
      }
    }
  }

  getToken(): string | null {
    return this.token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const config: RequestInit = {
      ...options,
      headers,
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        const error: ApiError = {
          message: errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`,
          code: errorData.error?.code,
          status: response.status,
          details: errorData.error?.details,
        };
        throw error;
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && 'status' in error) {
        throw error; // Re-throw API errors
      }
      
      // Handle network errors
      const networkError: ApiError = {
        message: 'Network error occurred',
        code: 'NETWORK_ERROR',
        details: { originalError: error },
      };
      throw networkError;
    }
  }

  // Authentication endpoints
  async login(credentials: LoginForm): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/api/v1/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });
    
    if (response.success && response.data?.token) {
      this.setToken(response.data.token);
    }
    
    return response;
  }

  async register(userData: RegisterForm): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/api/v1/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
    
    if (response.success && response.data?.token) {
      this.setToken(response.data.token);
    }
    
    return response;
  }

  async logout(): Promise<void> {
    this.setToken(null);
  }

  // User endpoints
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>('/api/v1/users/me');
  }

  async updateProfile(data: UserUpdateForm): Promise<ApiResponse<User>> {
    return this.request<ApiResponse<User>>('/api/v1/users/me', {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  async updatePreferences(data: UserPreferencesForm): Promise<ApiResponse> {
    return this.request<ApiResponse>('/api/v1/users/me/preferences', {
      method: 'PATCH',
      body: JSON.stringify(data),
    });
  }

  // Media endpoints
  async getMedia(id: string): Promise<ApiResponse<Media>> {
    return this.request<ApiResponse<Media>>(`/api/v1/media/${id}`);
  }

  async getMediaByTmdbId(tmdbId: number): Promise<ApiResponse<Media>> {
    return this.request<ApiResponse<Media>>(`/api/v1/media/tmdb/${tmdbId}`);
  }

  async cacheMedia(mediaData: any): Promise<ApiResponse<Media>> {
    return this.request<ApiResponse<Media>>('/api/v1/media', {
      method: 'POST',
      body: JSON.stringify(mediaData),
    });
  }

  // Watchlist endpoints
  async getWatchlists(page = 1, limit = 20): Promise<PaginatedResponse<Watchlist>> {
    return this.request<PaginatedResponse<Watchlist>>(
      `/api/v1/watchlists?page=${page}&limit=${limit}`
    );
  }

  async getWatchlist(id: string): Promise<ApiResponse<Watchlist>> {
    return this.request<ApiResponse<Watchlist>>(`/api/v1/watchlists/${id}`);
  }

  async createWatchlist(data: WatchlistCreate): Promise<ApiResponse<Watchlist>> {
    return this.request<ApiResponse<Watchlist>>('/api/v1/watchlists', {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  async addToWatchlist(watchlistId: string, data: WatchlistItemCreate): Promise<ApiResponse> {
    return this.request<ApiResponse>(`/api/v1/watchlists/${watchlistId}/items`, {
      method: 'POST',
      body: JSON.stringify(data),
    });
  }

  // TMDB endpoints (proxied through our API)
  async getPopularMovies(page = 1): Promise<TMDBResponse<TMDBMovie>> {
    return this.request<TMDBResponse<TMDBMovie>>(
      `/api/v1/tmdb/movies/popular?page=${page}`
    );
  }

  async getTrendingMovies(timeWindow: 'day' | 'week' = 'week'): Promise<TMDBResponse<TMDBMovie>> {
    return this.request<TMDBResponse<TMDBMovie>>(
      `/api/v1/tmdb/movies/trending?time_window=${timeWindow}`
    );
  }

  async getPopularTVShows(page = 1): Promise<TMDBResponse<TMDBTVShow>> {
    return this.request<TMDBResponse<TMDBTVShow>>(
      `/api/v1/tmdb/tv/popular?page=${page}`
    );
  }

  async getTrendingTVShows(timeWindow: 'day' | 'week' = 'week'): Promise<TMDBResponse<TMDBTVShow>> {
    return this.request<TMDBResponse<TMDBTVShow>>(
      `/api/v1/tmdb/tv/trending?time_window=${timeWindow}`
    );
  }

  async getMovieDetails(id: number): Promise<any> {
    return this.request<any>(`/api/v1/tmdb/movies/${id}`);
  }

  async getTVShowDetails(id: number): Promise<any> {
    return this.request<any>(`/api/v1/tmdb/tv/${id}`);
  }

  async searchMulti(query: string, page = 1): Promise<any> {
    return this.request<any>(
      `/api/v1/tmdb/search?query=${encodeURIComponent(query)}&page=${page}`
    );
  }

  async getGenres(type: 'movie' | 'tv' = 'movie'): Promise<TMDBGenresResponse> {
    return this.request<TMDBGenresResponse>(`/api/v1/tmdb/genres?type=${type}`);
  }

  async discoverContent(params: Record<string, any>): Promise<any> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        searchParams.append(key, String(value));
      }
    });
    
    return this.request<any>(`/api/v1/tmdb/discover?${searchParams.toString()}`);
  }

  // Health check
  async healthCheck(): Promise<any> {
    return this.request<any>('/health');
  }
}

// Create and export a singleton instance
export const apiClient = new ApiClient(API_BASE_URL);

// Export the class for testing or custom instances
export { ApiClient };

// Utility functions
export function getImageUrl(path: string | null, size: 'w200' | 'w300' | 'w500' | 'w780' | 'original' = 'w500'): string | null {
  if (!path) return null;
  return `https://image.tmdb.org/t/p/${size}${path}`;
}

export function formatDate(dateString: string | null): string {
  if (!dateString) return 'Unknown';
  
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  } catch {
    return 'Unknown';
  }
}

export function formatRuntime(minutes: number | null): string {
  if (!minutes) return 'Unknown';
  
  const hours = Math.floor(minutes / 60);
  const remainingMinutes = minutes % 60;
  
  if (hours === 0) {
    return `${remainingMinutes}m`;
  }
  
  return `${hours}h ${remainingMinutes}m`;
}

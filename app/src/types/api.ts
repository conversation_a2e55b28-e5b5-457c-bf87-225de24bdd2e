// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: {
    message: string;
    code?: string;
    details?: Record<string, any>;
  };
}

export interface PaginatedResponse<T> {
  success: boolean;
  data: {
    items: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}

// User Types
export interface User {
  id: string;
  email: string;
  username: string;
  name?: string | null;
  avatar?: string | null;
  createdAt: string;
  updatedAt: string;
  preferences?: UserPreferences;
}

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  region: string;
  adultContent: boolean;
  autoplay: boolean;
  notifications: boolean;
  preferredGenres?: string | null;
  excludedGenres?: string | null;
}

export interface AuthResponse {
  success: boolean;
  message: string;
  data?: {
    user: User;
    token: string;
  };
}

// Media Types
export interface Media {
  id: string;
  tmdbId: number;
  type: 'movie' | 'tv' | 'person';
  title: string;
  originalTitle?: string | null;
  overview?: string | null;
  releaseDate?: string | null;
  runtime?: number | null;
  status?: string | null;
  tagline?: string | null;
  homepage?: string | null;
  imdbId?: string | null;
  posterPath?: string | null;
  backdropPath?: string | null;
  voteAverage?: number | null;
  voteCount?: number | null;
  popularity?: number | null;
  genres?: string | null;
  genreIds?: string | null;
  keywords?: string | null;
  externalIds?: string | null;
  createdAt: string;
  updatedAt: string;
}

// Watchlist Types
export interface Watchlist {
  id: string;
  name: string;
  description?: string | null;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  itemsCount?: number;
  items?: WatchlistItem[];
}

export interface WatchlistItem {
  id: string;
  addedAt: string;
  notes?: string | null;
  media: Media;
}

export interface WatchlistCreate {
  name: string;
  description?: string;
  isPublic?: boolean;
}

export interface WatchlistItemCreate {
  mediaId: string;
  notes?: string;
}

// Rating Types
export interface Rating {
  id: string;
  rating: number;
  ratedAt: string;
  media: Media;
}

export interface RatingCreate {
  mediaId: string;
  rating: number;
}

// Review Types
export interface Review {
  id: string;
  title?: string | null;
  content: string;
  rating?: number | null;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  media: Media;
  user: {
    id: string;
    username: string;
    name?: string | null;
    avatar?: string | null;
  };
}

export interface ReviewCreate {
  mediaId: string;
  title?: string;
  content: string;
  rating?: number;
  isPublic?: boolean;
}

// Favorite Types
export interface Favorite {
  id: string;
  addedAt: string;
  media: Media;
}

export interface FavoriteCreate {
  mediaId: string;
}

// View History Types
export interface ViewHistory {
  id: string;
  viewedAt: string;
  duration?: number | null;
  progress?: number | null;
  media: Media;
}

export interface ViewHistoryCreate {
  mediaId: string;
  duration?: number;
  progress?: number;
}

// Search History Types
export interface SearchHistory {
  id: string;
  query: string;
  filters?: string | null;
  results?: number | null;
  searchedAt: string;
}

export interface SearchHistoryCreate {
  query: string;
  filters?: string;
  results?: number;
}

// TMDB Types (for direct TMDB API calls)
export interface TMDBMovie {
  id: number;
  title: string;
  original_title: string;
  overview: string;
  release_date: string;
  poster_path?: string | null;
  backdrop_path?: string | null;
  vote_average: number;
  vote_count: number;
  popularity: number;
  genre_ids: number[];
  adult: boolean;
  original_language: string;
  video: boolean;
}

export interface TMDBTVShow {
  id: number;
  name: string;
  original_name: string;
  overview: string;
  first_air_date: string;
  poster_path?: string | null;
  backdrop_path?: string | null;
  vote_average: number;
  vote_count: number;
  popularity: number;
  genre_ids: number[];
  adult: boolean;
  original_language: string;
  origin_country: string[];
}

export interface TMDBGenre {
  id: number;
  name: string;
}

export interface TMDBResponse<T> {
  page: number;
  results: T[];
  total_pages: number;
  total_results: number;
}

export interface TMDBGenresResponse {
  genres: TMDBGenre[];
}

// Error Types
export interface ApiError {
  message: string;
  code?: string;
  status?: number;
  details?: Record<string, any>;
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  email: string;
  username: string;
  password: string;
  name?: string;
}

export interface UserUpdateForm {
  name?: string;
  avatar?: string;
}

export interface UserPreferencesForm {
  theme?: 'light' | 'dark' | 'system';
  language?: string;
  region?: string;
  adultContent?: boolean;
  autoplay?: boolean;
  notifications?: boolean;
  preferredGenres?: string;
  excludedGenres?: string;
}

'use client';

import Link from 'next/link';
import { useState } from 'react';
import { Button } from '@/components/ui/button';

export function Header() {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  return (
    <header className='border-b bg-white'>
      <div className='container mx-auto px-4'>
        <div className='flex h-16 items-center justify-between'>
          {/* Logo */}
          <Link href='/' className='flex items-center space-x-2'>
            <span className='text-2xl'>🎬</span>
            <span className='text-xl font-bold text-gray-900'>Streama</span>
          </Link>

          {/* Navigation */}
          <nav className='hidden md:flex items-center space-x-6'>
            <Link
              href='/discover'
              className='text-gray-600 hover:text-gray-900 transition-colors'
            >
              Discover
            </Link>
            <Link
              href='/movies'
              className='text-gray-600 hover:text-gray-900 transition-colors'
            >
              Movies
            </Link>
            <Link
              href='/tv'
              className='text-gray-600 hover:text-gray-900 transition-colors'
            >
              TV Shows
            </Link>
            {isLoggedIn && (
              <Link
                href='/watchlists'
                className='text-gray-600 hover:text-gray-900 transition-colors'
              >
                My Lists
              </Link>
            )}
          </nav>

          {/* Auth Buttons */}
          <div className='flex items-center space-x-4'>
            {isLoggedIn ? (
              <>
                <Link
                  href='/profile'
                  className='text-gray-600 hover:text-gray-900 transition-colors'
                >
                  Profile
                </Link>
                <Button
                  variant='outline'
                  onClick={() => setIsLoggedIn(false)}
                >
                  Logout
                </Button>
              </>
            ) : (
              <>
                <Link href='/login'>
                  <Button variant='ghost'>Login</Button>
                </Link>
                <Link href='/register'>
                  <Button>Sign Up</Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}

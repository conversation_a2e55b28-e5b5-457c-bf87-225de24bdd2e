'use client';

import Link from 'next/link';
import { SearchBar } from './search-bar';
import { But<PERSON> } from '@/components/ui/button';
import { Film, Tv, Settings } from 'lucide-react';

export function Header() {
  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container mx-auto flex h-16 items-center justify-between px-4">
        {/* Logo */}
        <Link href="/" className="flex items-center space-x-2">
          <Film className="h-6 w-6" />
          <span className="text-xl font-bold">Streama</span>
        </Link>

        {/* Navigation */}
        <nav className="hidden md:flex items-center space-x-6">
          <Link 
            href="/discover/movie" 
            className="flex items-center space-x-1 text-sm font-medium transition-colors hover:text-primary"
          >
            <Film className="h-4 w-4" />
            <span>Movies</span>
          </Link>
          <Link 
            href="/discover/tv" 
            className="flex items-center space-x-1 text-sm font-medium transition-colors hover:text-primary"
          >
            <Tv className="h-4 w-4" />
            <span>TV Shows</span>
          </Link>
          <Link 
            href="/analytics" 
            className="text-sm font-medium transition-colors hover:text-primary"
          >
            Analytics
          </Link>
        </nav>

        {/* Search */}
        <div className="flex items-center space-x-4">
          <SearchBar className="w-64 hidden sm:block" />
          
          {/* Settings */}
          <Button variant="ghost" size="icon" asChild>
            <Link href="/settings">
              <Settings className="h-4 w-4" />
            </Link>
          </Button>
        </div>
      </div>

      {/* Mobile search */}
      <div className="border-t px-4 py-3 sm:hidden">
        <SearchBar />
      </div>
    </header>
  );
}

export default Header;

'use client';

import { TMDBMovie, TMDBTVShow } from '@/types/tmdb';
import { MediaCard } from './media-card';
import { Button } from '@/components/ui/button';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { useRef } from 'react';

interface MediaSectionProps {
  title: string;
  media: (TMDBMovie | TMDBTVShow)[];
  className?: string;
  showSeeAll?: boolean;
  seeAllHref?: string;
}

export function MediaSection({ 
  title, 
  media, 
  className,
  showSeeAll = false,
  seeAllHref
}: MediaSectionProps) {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -300,
        behavior: 'smooth'
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 300,
        behavior: 'smooth'
      });
    }
  };

  if (media.length === 0) {
    return null;
  }

  return (
    <section className={className}>
      <div className="mb-4 flex items-center justify-between">
        <h2 className="text-2xl font-bold">{title}</h2>
        <div className="flex items-center gap-2">
          {showSeeAll && seeAllHref && (
            <Button variant="outline" size="sm" asChild>
              <a href={seeAllHref}>See All</a>
            </Button>
          )}
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="icon"
              onClick={scrollLeft}
              className="h-8 w-8"
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="icon"
              onClick={scrollRight}
              className="h-8 w-8"
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <div
        ref={scrollContainerRef}
        className="flex gap-4 overflow-x-auto pb-4 scrollbar-hide"
        style={{
          scrollbarWidth: 'none',
          msOverflowStyle: 'none',
        }}
      >
        {media.map((item) => (
          <MediaCard 
            key={item.id} 
            media={item} 
            className="min-w-[200px] flex-shrink-0"
          />
        ))}
      </div>
    </section>
  );
}

export default MediaSection;

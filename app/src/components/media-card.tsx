import Image from 'next/image';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/card';
import { getImageUrl, formatDate } from '@/lib/api';
import type { TMDBMovie, TMDBTVShow } from '@/types/api';

interface MediaCardProps {
  media: TMDBMovie | TMDBTVShow;
  type: 'movie' | 'tv';
}

export function MediaCard({ media, type }: MediaCardProps) {
  const title = type === 'movie' ? (media as TMDBMovie).title : (media as TMDBTVShow).name;
  const releaseDate = type === 'movie' 
    ? (media as TMDBMovie).release_date 
    : (media as TMDBTVShow).first_air_date;
  
  const posterUrl = getImageUrl(media.poster_path, 'w300');
  const year = releaseDate ? new Date(releaseDate).getFullYear() : null;

  return (
    <Link href={`/${type}/${media.id}`}>
      <Card className='group cursor-pointer transition-all duration-200 hover:shadow-lg hover:scale-105'>
        <CardContent className='p-0'>
          <div className='relative aspect-[2/3] overflow-hidden rounded-t-lg'>
            {posterUrl ? (
              <Image
                src={posterUrl}
                alt={title}
                fill
                className='object-cover transition-transform duration-200 group-hover:scale-110'
                sizes='(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw'
              />
            ) : (
              <div className='w-full h-full bg-gray-200 flex items-center justify-center'>
                <span className='text-4xl text-gray-400'>🎬</span>
              </div>
            )}
            
            {/* Rating Badge */}
            {media.vote_average > 0 && (
              <div className='absolute top-2 right-2 bg-black/70 text-white text-xs px-2 py-1 rounded-full'>
                ⭐ {media.vote_average.toFixed(1)}
              </div>
            )}
          </div>
          
          <div className='p-4'>
            <h3 className='font-semibold text-sm line-clamp-2 mb-1 group-hover:text-blue-600 transition-colors'>
              {title}
            </h3>
            {year && (
              <p className='text-xs text-gray-500'>
                {year}
              </p>
            )}
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}

interface MediaGridProps {
  media: (TMDBMovie | TMDBTVShow)[];
  type: 'movie' | 'tv';
  title?: string;
}

export function MediaGrid({ media, type, title }: MediaGridProps) {
  if (media.length === 0) {
    return (
      <div className='text-center py-12'>
        <span className='text-4xl mb-4 block'>🎬</span>
        <p className='text-gray-500'>No content found</p>
      </div>
    );
  }

  return (
    <div className='space-y-6'>
      {title && (
        <h2 className='text-2xl font-bold text-gray-900'>{title}</h2>
      )}
      <div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4'>
        {media.map((item) => (
          <MediaCard key={item.id} media={item} type={type} />
        ))}
      </div>
    </div>
  );
}

interface MediaSectionProps {
  media: (TMDBMovie | TMDBTVShow)[];
  type: 'movie' | 'tv';
  title: string;
  showSeeAll?: boolean;
  seeAllHref?: string;
}

export function MediaSection({ media, type, title, showSeeAll, seeAllHref }: MediaSectionProps) {
  return (
    <section className='space-y-4'>
      <div className='flex items-center justify-between'>
        <h2 className='text-2xl font-bold text-gray-900'>{title}</h2>
        {showSeeAll && seeAllHref && (
          <Link 
            href={seeAllHref}
            className='text-blue-600 hover:text-blue-700 text-sm font-medium'
          >
            See All →
          </Link>
        )}
      </div>
      
      {media.length === 0 ? (
        <div className='text-center py-8'>
          <span className='text-4xl mb-4 block'>🎬</span>
          <p className='text-gray-500'>No content available</p>
        </div>
      ) : (
        <div className='overflow-x-auto'>
          <div className='flex space-x-4 pb-4'>
            {media.slice(0, 10).map((item) => (
              <div key={item.id} className='flex-none w-48'>
                <MediaCard media={item} type={type} />
              </div>
            ))}
          </div>
        </div>
      )}
    </section>
  );
}
